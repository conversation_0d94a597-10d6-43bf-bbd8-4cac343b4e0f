<script setup>
import HawkEditableName from '~/common/components/molecules/hawk-editable-name.vue';

const emit = defineEmits(['close']);

const state = reactive({
  // NOTE: widget_details might be a prop because we may need to edit the configuration options for existing widgets.
  widget_details: {
    name: 'hello',
  },
  is_renaming: false,
});

function handleNameChange(name) {
  state.widget_details.name = name;
  state.is_renaming = false;
}
</script>

<template>
  <HawkModalContainer content_class="h-full w-full rounded-none">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <div class="font-semibold text-lg">
            <HawkEditableName
              v-if="state.is_renaming"
              class="max-w-[50vw]"
              :name="state.widget_details.name"
              :input_classes="{
                TextElement: {
                  input: 'font-semibold !text-xl !p-0',
                  inputContainer: 'border-0 border-b',
                },
                ElementLayout: {
                  innerWrapper: 'border-b',
                },
              }"
              @close="state.is_renaming = false"
              @update="handleNameChange($event)"
            />
            <div v-else class="group flex items-center gap-3 text-xl text-gray-900 font-semibold max-w-[65vw]" @click="state.is_renaming = true">
              <span>
                {{ state.widget_details.name }}
              </span>
              <IconHawkEditOne class="w-4 h-4 group-hover:visible invisible cursor-pointer" @click="state.is_renaming = true" />
            </div>
          </div>
        </template>
      </HawkModalHeader>
      <HawkModalContent class="">
        CONTENT
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
